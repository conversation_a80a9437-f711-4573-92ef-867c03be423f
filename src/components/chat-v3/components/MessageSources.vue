<template>
  <div v-if="processedDocs && processedDocs.length > 0" class="file-sources">
    <el-collapse v-model="activeDocsPanel" accordion>
      <el-collapse-item name="docs">
        <template slot="title">
          <el-tag size="small" type="primary">文件来源</el-tag>
        </template>
        <div class="docs-list">
          <div
            v-for="(doc, index) in processedDocs"
            :key="index"
            class="doc-item"
          >
            <div class="doc-content">
              <el-link
                type="primary"
                :underline="false"
                @click="handlePreview(doc)"
                class="doc-link"
              >
                {{ doc.fileName }}
              </el-link>

              <!-- PDF 文档的页码列表 -->
              <template v-if="doc.fileType === 'pdf' && doc.pages && doc.pages.length > 0">
                <span class="pages-label">页码：</span>
                <el-link
                  v-for="(page, pageIndex) in doc.pages"
                  :key="pageIndex"
                  type="info"
                  :underline="false"
                  @click="handlePreview(doc, page)"
                  class="page-link"
                >
                  {{ page }}
                </el-link>
              </template>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { preview } from '@/utils/fileStreamPreview'

export default {
  name: 'MessageSources',
  props: {
    docs: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeDocsPanel: 'docs' // 默认展开
    }
  },
  computed: {
    processedDocs() {
      if (!this.docs?.length) return [];

      const docMap = new Map();

      this.docs.forEach(docStr => {
        try {
          const doc = JSON.parse(docStr);
          const isPdf = doc.filename?.toLowerCase().endsWith('.pdf');
          const fileName = doc.filename;

          if (docMap.has(fileName)) {
            if (isPdf && doc.page) {
              const existing = docMap.get(fileName);
              if (!existing.pages.includes(doc.page)) {
                existing.pages.push(doc.page);
              }
            }
          } else {
            docMap.set(fileName, {
              fileName,
              link: doc.url,
              page: doc.page,
              fileType: isPdf ? 'pdf' : 'word',
              pages: isPdf && doc.page ? [doc.page] : []
            });
          }
        } catch (error) {
          console.error('解析文档失败:', error);
        }
      });

      return Array.from(docMap.values());
    }
  },
  methods: {
    // 统一的文档预览处理
    async handlePreview(doc, page = null) {
      if (!doc || !doc.link) {
        this.$message.warning('文件链接不存在');
        return;
      }

      if (doc.fileType === 'pdf') {
        this.openPdfPreview(doc.link, page);
      } else {
        await preview(doc.link, doc.fileName);
      }
    },

    // 统一的 PDF 预览方法
    openPdfPreview(link, page = null) {
      let proxyUrl = link;

      if (link.includes('knowledge_base/download_doc')) {
        try {
          const url = new URL(link);
          const knowledgeBaseName = url.searchParams.get('knowledge_base_name');
          const fileName = url.searchParams.get('file_name');
          proxyUrl =
            `/prod-api/techDocManage/knowledge_base/download_doc?knowledge_base_name=${encodeURIComponent(knowledgeBaseName)}&file_name=${encodeURIComponent(fileName)}`;
        } catch (error) {
          console.error('解析文档链接失败:', error);
          this.$message.error('无效的文档链接');
          return;
        }
      }

      const pageParam = page ? `#page=${page}` : '';
      const viewerPath = '/static/pdf/web/viewer.html';
      const fileUrl = `${viewerPath}?file=${encodeURIComponent(proxyUrl)}`;
      const finalUrl = `${fileUrl}${pageParam}`;

      window.open(finalUrl, '_blank');
    }
  }
}
</script>

<style lang="scss" scoped>
.file-sources {
  :deep(.el-collapse) {
    border: none;

    .el-collapse-item {
      border-bottom: none;

      .el-collapse-item__header {
        height: auto;
        line-height: 1.5;
        padding: 8px 0;
        border-bottom: none;
        background: transparent;
        font-size: 14px;

        &:hover {
          background: #f5f7fa;
        }
      }

      .el-collapse-item__content {
        padding: 0 0 12px 0;
        color: #8b8b8b;
      }
    }
  }

  .docs-list {
    .doc-item {
      margin-bottom: 4px;
      padding: 2px 0;
      border-bottom: none;

      &:last-child {
        margin-bottom: 0;
      }

      .doc-content {
        display: flex;
        align-items: baseline;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 0; /* 覆盖全局样式 */
        margin-left: 0; /* 覆盖全局样式 */
      }

      .doc-link {
        font-size: 14px;

        &:hover {
          color: #66b1ff;
        }
      }

      .pages-label {
        color: #666;
        font-size: 12px;
        margin-right: 4px;
      }

      .page-link {
        font-size: 12px;
        padding: 2px 6px;
        background: #f5f7fa;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
        text-align: left;

        &:hover {
          background: #ecf5ff;
          border-color: #b3d8ff;
          color: #409eff;
        }
      }
    }
  }
}
</style>

<template>
  <div class="line-chart-container">
    <div ref="chartContainer" class="chart-wrapper"></div>
  </div>
</template>

<script>
import chartMixin from './chartMixin.js'

export default {
  name: 'LineChart',
  mixins: [chartMixin],
  methods: {

    updateChart() {
      if (!this.chart || !this.data || this.data.length === 0) return

      // 使用统一的 label/value 字段
      const categories = this.data.map(item => item.label)
      const values = this.data.map(item => item.value)

      const option = {
        title: {
          text: this.title,
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: categories,
          axisLabel: {
            fontSize: 12,
            rotate: categories.some(cat => cat.length > 4) ? 45 : 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '数值',
            type: 'line',
            data: values,
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            },
            lineStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(64, 158, 255, 0.3)'
                }, {
                  offset: 1, color: 'rgba(64, 158, 255, 0.1)'
                }]
              }
            },
            label: {
              show: true,
              position: 'top',
              fontSize: 12
            }
          }
        ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.line-chart-container {
  width: 100%;

  .chart-wrapper {
    width: 100%;
    height: 300px;
  }
}
</style>

<template>
  <div class="bar-chart-container">
    <div ref="chartContainer" class="chart-wrapper"></div>
  </div>
</template>

<script>
import chartMixin from './chartMixin.js'

export default {
  name: 'Bar<PERSON>hart',
  mixins: [chartMixin],
  methods: {

    updateChart() {
      if (!this.chart || !this.data || this.data.length === 0) return

      // 使用统一的 label/value 字段
      const categories = this.data.map(item => item.label)
      const values = this.data.map(item => item.value)

      const option = {
        title: {
          text: this.title,
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            fontSize: 12,
            rotate: categories.some(cat => cat.length > 4) ? 45 : 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '数值',
            type: 'bar',
            data: values,
            itemStyle: {
              color: '#409EFF'
            },
            label: {
              show: true,
              position: 'top',
              fontSize: 12
            }
          }
        ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.bar-chart-container {
  width: 100%;

  .chart-wrapper {
    width: 100%;
    height: 300px;
  }
}
</style>

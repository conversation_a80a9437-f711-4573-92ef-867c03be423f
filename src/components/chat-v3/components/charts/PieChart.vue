<template>
  <div class="pie-chart-container">
    <div ref="chartContainer" class="chart-wrapper"></div>
  </div>
</template>

<script>
import chartMixin from './chartMixin.js'

export default {
  name: 'PieChart',
  mixins: [chartMixin],
  methods: {

    updateChart() {
      if (!this.chart || !this.data || this.data.length === 0) return

      // 转换为ECharts格式
      const chartData = this.data.map(item => ({
        name: item.label,
        value: item.value
      }))

      const option = {
        title: {
          text: this.title,
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: this.title || '数据统计',
            type: 'pie',
            radius: '50%',
            center: ['50%', '60%'],
            data: chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              fontSize: 12
            }
          }
        ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.pie-chart-container {
  width: 100%;

  .chart-wrapper {
    width: 100%;
    height: 300px;
  }
}
</style>

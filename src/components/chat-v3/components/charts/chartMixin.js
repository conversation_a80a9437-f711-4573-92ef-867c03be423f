import * as echarts from 'echarts'

export default {
  props: {
    data: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.initChart()
  },
  beforeD<PERSON>roy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  watch: {
    data: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
    }
  }
}

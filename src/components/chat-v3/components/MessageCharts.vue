<template>
  <div v-if="processedChart" class="message-charts">
    <el-collapse v-model="activeChartsPanel" accordion>
      <el-collapse-item name="charts">
        <template slot="title">
          <el-tag size="small" type="success">图表数据</el-tag>
        </template>
        <div class="charts-container">
          <div class="chart-item">
            <!-- 饼状图 -->
            <PieChart
              v-if="processedChart.type === 'pie'"
              :data="processedChart.data"
              :title="processedChart.title"
            />

            <!-- 柱状图 -->
            <BarChart
              v-if="processedChart.type === 'bar'"
              :data="processedChart.data"
              :title="processedChart.title"
            />

            <!-- 折线图 -->
            <LineChart
              v-if="processedChart.type === 'line'"
              :data="processedChart.data"
              :title="processedChart.title"
            />

            <!-- 表格展示 -->
            <div v-if="shouldShowTable(processedChart)" class="chart-table">
              <h4 v-if="processedChart.type === 'table'" class="table-title">{{ processedChart.title }}</h4>
              <el-table
                :data="processedChart.data"
                size="small"
                border
                style="width: 100%; margin-top: 8px;"
              >
                <el-table-column
                  v-for="(value, key) in getTableColumns(processedChart.data)"
                  :key="key"
                  :prop="key"
                  :label="getColumnLabel(key)"
                  align="center"
                />
              </el-table>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import PieChart from './charts/PieChart.vue'
import BarChart from './charts/BarChart.vue'
import LineChart from './charts/LineChart.vue'

export default {
  name: 'MessageCharts',
  components: {
    PieChart,
    BarChart,
    LineChart
  },
  props: {
    chartData: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      activeChartsPanel: 'charts' // 默认展开
    }
  },
  computed: {
    processedChart() {
      if (!this.chartData) return null

      try {
        return JSON.parse(this.chartData)
      } catch (error) {
        console.error('解析图表数据失败:', error, '原始数据:', this.chartData)
        return null
      }
    }
  },
  methods: {
    shouldShowTable(chartData) {
      // table 类型：只显示表格
      if (chartData.type === 'table') {
        return true
      }
      // 图表类型：根据 showTable 配置决定是否同时显示表格
      return chartData.showTable === true
    },

    getTableColumns(data) {
      if (!data || data.length === 0) return {}
      return data[0]
    },

    getColumnLabel(key) {
      // 使用数据中的 columnLabels 映射
      if (this.processedChart && this.processedChart.columnLabels) {
        return this.processedChart.columnLabels[key] || key
      }

      // 如果没有 columnLabels，使用友好的默认值
      const defaultLabels = {
        'label': '名称',
        'value': '数值'
      }
      return defaultLabels[key] || key
    }
  }
}
</script>

<style lang="scss" scoped>
.message-charts {
  :deep(.el-collapse) {
    border: none;

    .el-collapse-item {
      border-bottom: none;

      .el-collapse-item__header {
        height: auto;
        line-height: 1.5;
        padding: 8px 0;
        border-bottom: none;
        background: transparent;
        font-size: 14px;

        &:hover {
          background: #f5f7fa;
        }
      }

      .el-collapse-item__content {
        padding: 0 0 12px 0;
      }
    }
  }

  .charts-container {
    background: #fafafa;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 16px;
    width: 100%;
  }

  .chart-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    &:not(:last-child) {
      border-bottom: 1px solid #e4e7ed;
      padding-bottom: 24px;
    }
  }

  .chart-table {
    .table-title {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-table) {
      font-size: 12px;

      .el-table__header {
        th {
          background: #f5f7fa;
          font-weight: 600;
        }
      }
    }
  }
}
</style>

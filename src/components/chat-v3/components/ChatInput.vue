<template>
  <div class="input-area">
    <textarea
      ref="textarea"
      v-model="inputMessage"
      placeholder="请输入您的问题"
      @keydown.enter.prevent="handleSend"
    ></textarea>

    <div class="input-toolbar">
      <div class="toolbar-left">
        <ActiveFilesToolbar v-if="isFileMode" />
        <KnowledgeToolbar v-else />
      </div>

      <div class="toolbar-right">
        <ActionButtons
          :can-send="canSend"
          :is-sending="isSending"
          :is-streaming="isStreaming"
          :can-use-voice="canUseVoice"
          :is-voice-recording="isVoiceRecording"
          :voice-button-title="voiceButtonTitle"
          @send="handleSend"
          @stop="handleStop"
          @voice-start="handleVoiceStart"
          @voice-result="handleVoiceResult"
          @voice-stop="handleVoiceStop"
          @voice-blank="handleVoiceBlank"
          @voice-complete="handleVoiceComplete"
          @voice-failed="handleVoiceFailed"
        />

      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';
import ActionButtons from './ActionButtons.vue';
import KnowledgeToolbar from './KnowledgeToolbar.vue';
import ActiveFilesToolbar from './ActiveFilesToolbar.vue';

export default {
  name: 'ChatInput',
  components: {
    ActionButtons,
    KnowledgeToolbar,
    ActiveFilesToolbar
  },

  data() {
    return {
      inputMessage: '',
      isVoiceRecording: false
    };
  },
  computed: {
    ...mapState('chat-v3', ['isSending', 'currentKnowledgeId', 'activeFiles']),
    ...mapGetters('chat-v3', ['canSendMessage', 'isStreaming', 'isFileMode']),

    canSend() {
      return this.canSendMessage && !this.isVoiceRecording && this.inputMessage.trim().length > 0;
    },

    canUseVoice() {
      return !this.isSending && !this.isStreaming;
    },

    voiceButtonTitle() {
      if (this.isSending || this.isStreaming) {
        return 'AI正在回答中，请稍候';
      }
      if (this.isVoiceRecording) {
        return '正在录音...点击停止';
      }
      return '点击开始语音输入';
    }
  },
  methods: {
    ...mapActions('chat-v3', ['sendMessage', 'stopStreamChat']),

    async handleSend() {
      if (!this.canSend) return;

      try {
        // 通知父组件重置自动滚动
        this.$emit('message-sending');

        await this.sendMessage({
          text: this.inputMessage.trim(),
          knowledgeId: this.currentKnowledgeId
        });
        this.inputMessage = '';

        // 成功后，触发历史列表刷新事件
        this.$emit('refresh-history');
      } catch (error) {
        this.$message.error('发送消息失败');
      }
    },

    handleStop() {
      this.stopStreamChat();
      this.$message.info('已停止生成');
    },

    stopVoiceRecording() {
      this.isVoiceRecording = false;
    },

    handleVoiceStart() {
      if (!this.canUseVoice) return;
      this.isVoiceRecording = true;
    },

    handleVoiceResult(text) {
      this.inputMessage = text;
    },

    handleVoiceStop() {
      this.stopVoiceRecording();
    },

    handleVoiceBlank() {
      this.$message.warning('没有录到内容，请重试');
      this.stopVoiceRecording();
    },

    handleVoiceComplete(text) {
      this.inputMessage = text;
      this.stopVoiceRecording();
    },

    handleVoiceFailed() {
      this.$message.error('语音识别失败，请重试');
      this.stopVoiceRecording();
    }
  }
};
</script>

<style lang="scss" scoped>
.input-area {
  background: #fff;
  border-radius: 16px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.12);
  flex-shrink: 0;
  margin-top: auto;
  position: relative;
  z-index: 10;

  textarea {
    width: 100%;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    height: 80px;
  }
}

.input-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #e8e8e8;
}

.toolbar-left, .toolbar-right {
  display: flex;
  gap: 10px;
}
</style>

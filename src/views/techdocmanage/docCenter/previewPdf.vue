<template>
  <!-- <div class="container" v-loading="loading" element-loading-text="预览文件加载中">
    <iframe
      v-if="isShowPdf"
      :src="pdfSrc"
      frameborder="0"
      width="100%"
      height="100%"
    ></iframe>
  </div> -->
  <div class="container">
    <iframe :src="pdfSrc" frameborder="0" width="100%" height="100%"></iframe>
  </div>
</template>

<script>
import userService from "@/api/techdocmanage/docCenter/user";
import { getConfigKey } from "@/api/system/config";
import serviceKnowledge from "@/api/knowledge.js";
export default {
  data() {
    return {
      fileId: "",
      fileName: "",
      initCurrentIP: "",
      loading: false,
      isShowPdf: false,
      pdfSrc: "", // 用于存储 PDF 文件的 URL
      retryCount: 0, // 重试次数
      maxRetries: 10, // 最大重试次数
      kkFileView: "",
    };
  },
  mounted() {
    this.fileId = this.$route.params.docId || this.$store.state.common.previewFileId;
    this.getConfigKey("kkFileView").then((response) => {
      this.kkFileView = response.msg;
      this.openFile();
    });
  },
  methods: {
    async openFile() {
      const res = await serviceKnowledge.getKKFileUrl({
        id: this.fileId,
      });
      if (res.code == 200) {
        let previewUrl = `${res.data.url}`;

        // 获取文件后缀
       const fileExtension = this.getFileExtension(previewUrl);
       // 检查是否为Office文件类型
       if (this.isOfficeFile(fileExtension)) {
          this.$message({
            message: 'Office文件预览可能存在兼容性问题，如长时间无法预览，请下载后本地查看。',
            type: 'success',
            duration: 5000,
            showClose: true
          });
       }
        
        this.pdfSrc =
          this.kkFileView +
          "/onlinePreview?url=" +
          encodeURIComponent(Base64.encode(previewUrl));
        console.log("最后地址", this.pdfSrc);
        console.log("预览地址", previewUrl);
      }
    },
    // 获取文件后缀
    getFileExtension(url) {
      if (!url) return '';
      
      // 移除URL参数部分
      const urlWithoutParams = url.split('?')[0];
      
      // 获取文件名部分
      const fileName = urlWithoutParams.split('/').pop();
      
      // 获取文件后缀
      const extension = fileName.split('.').pop();
      
      return extension ? extension.toLowerCase() : '';
    },

    // 判断是否为Office文件
    isOfficeFile(extension) {
      const officeExtensions = [
        'ppt', 'pptx',     // PowerPoint
        'doc', 'docx',     // Word
        'xls', 'xlsx'      // Excel
      ];
      
      return officeExtensions.includes(extension);
    },
  },
  beforeDestroy() {},
};
</script>

<style lang="scss" scoped>
.container {
  height: calc(100% - 20px);
  padding: 24px;
  box-sizing: border-box;
  background-color: #f0f2f5;
  position: relative;
}
</style>
